Stack trace:
Frame         Function      Args
0007FFFFB750  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFB750, 0007FFFFA650) msys-2.0.dll+0x1FE8E
0007FFFFB750  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA28) msys-2.0.dll+0x67F9
0007FFFFB750  000210046832 (000210286019, 0007FFFFB608, 0007FFFFB750, 000000000000) msys-2.0.dll+0x6832
0007FFFFB750  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB750  000210068E24 (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA30  00021006A225 (0007FFFFB760, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFEAA90000 ntdll.dll
7FFFE8A20000 KERNEL32.DLL
7FFFE7CA0000 KERNELBASE.dll
7FFFE9340000 USER32.dll
7FFFE8630000 win32u.dll
7FFFE9B60000 GDI32.dll
7FFFE8080000 gdi32full.dll
7FFFE7B80000 msvcp_win.dll
7FFFE84E0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFFE8960000 advapi32.dll
7FFFE9F40000 msvcrt.dll
7FFFE88B0000 sechost.dll
7FFFE8600000 bcrypt.dll
7FFFE8720000 RPCRT4.dll
7FFFE7300000 CRYPTBASE.DLL
7FFFE7C20000 bcryptPrimitives.dll
7FFFE9080000 IMM32.DLL
